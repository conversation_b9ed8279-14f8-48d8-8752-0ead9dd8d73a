@echo off
setlocal enabledelayedexpansion

REM MsgPilot Docker 构建脚本 (Windows)
set IMAGE_NAME=msgpilot
set TAG=%1
if "%TAG%"=="" set TAG=latest
set FULL_IMAGE_NAME=%IMAGE_NAME%:%TAG%

echo 开始构建 MsgPilot Docker 镜像...

REM 检查 Docker 是否运行
docker info >nul 2>&1
if errorlevel 1 (
    echo 错误: Docker 未运行，请先启动 Docker
    exit /b 1
)

REM 构建镜像
echo 正在构建镜像: %FULL_IMAGE_NAME%
docker build -t "%FULL_IMAGE_NAME%" .

if errorlevel 1 (
    echo 镜像构建失败
    exit /b 1
) else (
    echo 镜像构建成功: %FULL_IMAGE_NAME%
    
    REM 显示镜像信息
    echo 镜像信息:
    docker images %IMAGE_NAME%
    
    echo 构建完成！
    echo 运行命令:
    echo   docker run -p 8080:8080 %FULL_IMAGE_NAME%
    echo   或者使用 docker-compose up
)
