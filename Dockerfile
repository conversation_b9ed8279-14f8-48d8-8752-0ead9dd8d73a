# 多阶段构建 Dockerfile for MsgPilot
# 阶段1: 前端构建
FROM node:18-alpine AS frontend-builder

# 设置工作目录
WORKDIR /app/frontend

# 复制前端依赖文件
COPY frontend/package*.json ./

# 安装依赖（包括开发依赖，构建时需要）
RUN npm ci

# 复制前端源码
COPY frontend/ ./

# 构建前端静态文件
RUN npm run build

# 阶段2: 后端构建
FROM golang:1.23-alpine AS backend-builder

# 安装必要的工具
RUN apk add --no-cache git ca-certificates tzdata

# 设置工作目录
WORKDIR /app

# 复制 Go 模块文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 安装 swag 工具用于生成 API 文档
RUN go install github.com/swaggo/swag/cmd/swag@latest

# 复制源码
COPY . .

# 从前端构建阶段复制静态文件到后端
COPY --from=frontend-builder /app/frontend/dist ./frontend/dist

# 生成 Swagger 文档
RUN swag init

# 构建后端可执行文件
RUN CGO_ENABLED=1 GOOS=linux go build -a -installsuffix cgo -o main .

# 阶段3: 最终镜像
FROM alpine:latest

# 安装运行时依赖
RUN apk --no-cache add ca-certificates tzdata sqlite

# 创建非root用户
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# 设置工作目录
WORKDIR /app

# 从构建阶段复制可执行文件
COPY --from=backend-builder /app/main .

# 从构建阶段复制前端静态文件
COPY --from=backend-builder /app/frontend/dist ./frontend/dist

# 复制配置文件（如果有的话）
COPY --from=backend-builder /app/config ./config

# 创建数据目录
RUN mkdir -p /app/data && \
    chown -R appuser:appgroup /app

# 切换到非root用户
USER appuser

# 暴露端口（根据您的应用配置调整）
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# 启动应用
CMD ["./main"]
