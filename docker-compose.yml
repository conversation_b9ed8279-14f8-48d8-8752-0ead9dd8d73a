version: '3.8'

services:
  msgpilot:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: msgpilot-app
    ports:
      - "8080:8080"
    environment:
      - PORT=8080
      - GIN_MODE=release
    volumes:
      # 持久化数据库文件
      - msgpilot_data:/app/data
      # 如果需要自定义配置文件，可以挂载
      # - ./config:/app/config:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  msgpilot_data:
    driver: local
